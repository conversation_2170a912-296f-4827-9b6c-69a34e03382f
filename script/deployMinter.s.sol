//SPDX-License-Identifier: Unlicense
pragma solidity ^0.8.0;

import { Script } from "forge-std/Script.sol"; // Gives vm and console
import 'forge-std/console.sol';
import {frxETH} from "../src/frxETH.sol";
import {sfrxETH, ERC20} from "../src/sfrxETH.sol";
import {stPlumeMinter} from "../src/stPlumeMinter.sol";
import {stPlumeRewards} from "../src/stPlumeRewards.sol";
import {OperatorRegistry} from "../src/OperatorRegistry.sol";
import "openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol";
import "openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol";

contract Deploy is Script {
    address constant TIMELOCK_ADDRESS = ******************************************;
    uint32 constant REWARDS_CYCLE_LENGTH = 7 days;
    address constant PLUME_STAKING = ******************************************;
    address constant deployer = ******************************************;

    function run() public {
        console.log('Deployer:', deployer);
        vm.startBroadcast(deployer);

        frxETH fe = new frxETH(deployer, TIMELOCK_ADDRESS);
        // // sfrxETH sfe = new sfrxETH(ERC20(address(fe)), REWARDS_CYCLE_LENGTH);
        ProxyAdmin admin = new ProxyAdmin();
        // Encode initializer
        stPlumeMinter impl = new stPlumeMinter();
        // bytes memory initData = abi.encodeWithSignature("initialize(address,address, address, address, address)", address(fe), address(0), msg.sender, TIMELOCK_ADDRESS, PLUME_STAKING);
        TransparentUpgradeableProxy proxy = new TransparentUpgradeableProxy(address(impl), address(admin), bytes(""));
        stPlumeMinter fem = stPlumeMinter(payable(address(proxy)));
        fem.initialize(address(fe), deployer, TIMELOCK_ADDRESS, PLUME_STAKING);

        stPlumeRewards implRewards = new stPlumeRewards();
        // bytes memory initData2 = abi.encodeWithSignature("initialize(address,address, address)", address(fe), address(fem), msg.sender);
        TransparentUpgradeableProxy proxyRewards = new TransparentUpgradeableProxy(address(implRewards), address(admin), bytes(""));
        stPlumeRewards femRewards = stPlumeRewards(payable(address(proxyRewards)));
        femRewards.initialize(address(fe), address(fem), deployer);
        
        OperatorRegistry.Validator[] memory validators = new OperatorRegistry.Validator[](5);
        validators[0] = OperatorRegistry.Validator(3);
        validators[1] = OperatorRegistry.Validator(9);
        validators[2] = OperatorRegistry.Validator(8);
        validators[3] = OperatorRegistry.Validator(5);
        validators[4] = OperatorRegistry.Validator(1);
        // Post deploy
        console.log('Deployer:', deployer);
        fe.addMinter(address(fem));
        fem.setStPlumeRewards(address(femRewards));
        fe.updateStPlumeRewards(address(femRewards));
        fem.addValidators(validators);
        femRewards.grantRole(femRewards.MINTER_ROLE(), ******************************************); //approve keeper

        console.log("Minter deployed at", address(fem));
        console.log("Minter added to frxETH at", address(fe));
        console.log("Minter added to frETh Rewards at", address(femRewards));
        
        vm.stopBroadcast();
    }
}



// == Logs ==
//   Deployer: ******************************************
//   Deployer: ******************************************
//   Minter deployed at ******************************************
//   Minter added to frxETH at ******************************************
//   Minter added to sfrxETH at ******************************************


// Mainnet test
// == Logs ==
//   Deployer: ******************************************
//   Deployer: ******************************************
//   Minter deployed at ******************************************
//   Minter added to frxETH at ******************************************

// mainnet test 2
// == Logs ==
//   Deployer: ******************************************
//   Deployer: ******************************************
//   Minter deployed at ******************************************
//   Minter added to frxETH at ******************************************
//   Minter added to frETh Rewards at ******************************************

// Mainnet Test Final
// Deployer: ******************************************
//   Deployer: ******************************************
//   Minter deployed at ******************************************
//   Minter added to frxETH at ******************************************
//   Minter added to frETh Rewards at ******************************************
//   Proxy admin is at ******************************************

// Mainnet Main
// == Logs ==
//   Deployer: ******************************************
//   Deployer: ******************************************
//   Minter deployed at ******************************************
//   Minter added to frxETH at ******************************************
//   Minter added to frETh Rewards at ******************************************
//   Proxy admin is at ******************************************

