//SPDX-License-Identifier: Unlicense
pragma solidity ^0.8.0;

import { <PERSON>rip<PERSON> } from "forge-std/Script.sol"; // Gives vm and console
import {frxETH} from "../src/frxETH.sol";
import {sfrxETH, ERC20} from "../src/sfrxETH.sol";
import {frxETHMinter, OperatorRegistry} from "../src/frxETHMinter.sol";

contract Deploy is Script {
    address constant OWNER_ADDRESS = ******************************************;
    address constant TIMELOCK_ADDRESS = ******************************************;

    address constant DEPOSIT_CONTRACT_ADDRESS = ******************************************;
    bytes WITHDRAWAL_CREDENTIALS;
    uint32 constant REWARDS_CYCLE_LENGTH = 604800;

    function run() public {
        vm.startBroadcast();
        WITHDRAWAL_CREDENTIALS = vm.envBytes('VALIDATOR_MAINNET_WITHDRAWAL_CREDENTIALS');

        frxETH fe = new frxETH(OWNER_ADDRESS, TIMELOCK_ADDRESS);
        sfrxETH sfe = new sfrxETH(ERC20(address(fe)), REWARDS_CYCLE_LENGTH);
        frxETHMinter fem = new frxETHMinter(DEPOSIT_CONTRACT_ADDRESS, address(fe), address(sfe), OWNER_ADDRESS, TIMELOCK_ADDRESS, WITHDRAWAL_CREDENTIALS);
        
        // // Post deploy
        // fe.addMinter(address(fem));
        
        vm.stopBroadcast();
    }
}
