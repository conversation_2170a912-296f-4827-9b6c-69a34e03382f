[submodule "lib/forge-std"]
	path = lib/forge-std
	url = https://github.com/foundry-rs/forge-std
	# ignore = dirty
[submodule "lib/openzeppelin-contracts"]
	path = lib/openzeppelin-contracts
	url = https://github.com/OpenZeppelin/openzeppelin-contracts/
	# ignore = dirty
[submodule "lib/solmate"]
	path = lib/solmate
	url = https://github.com/Rari-Capital/solmate
	# ignore = dirty
[submodule "lib/ERC4626"]
	path = lib/ERC4626
	url = https://github.com/corddry/ERC4626
	# ignore = dirty