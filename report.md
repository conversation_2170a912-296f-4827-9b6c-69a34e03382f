# Aderyn Analysis Report

This report was generated by [<PERSON><PERSON><PERSON>](https://github.com/<PERSON><PERSON>rin/aderyn), a static analysis tool built by [<PERSON><PERSON>rin](https://cyfrin.io), a blockchain security company. This report is not a substitute for manual audit or security review. It should not be relied upon for any purpose other than to assist in the identification of potential security vulnerabilities.
# Table of Contents

- [Summary](#summary)
  - [Files Summary](#files-summary)
  - [Files Details](#files-details)
  - [Issue Summary](#issue-summary)
- [High Issues](#high-issues)
  - [H-1: Unsafe Casting](#h-1-unsafe-casting)
  - [H-2: Contract Name Reused in Different Files](#h-2-contract-name-reused-in-different-files)
  - [H-3: Uninitialized State Variables](#h-3-uninitialized-state-variables)
  - [H-4: Sending native Eth is not protected from these functions.](#h-4-sending-native-eth-is-not-protected-from-these-functions)
  - [H-5: Return value of the function call is not checked.](#h-5-return-value-of-the-function-call-is-not-checked)
  - [H-6: Contract locks E<PERSON> without a withdraw function.](#h-6-contract-locks-ether-without-a-withdraw-function)
- [Low Issues](#low-issues)
  - [L-1: Centralization Risk for trusted owners](#l-1-centralization-risk-for-trusted-owners)
  - [L-2: Deprecated OpenZeppelin functions should not be used](#l-2-deprecated-openzeppelin-functions-should-not-be-used)
  - [L-3: Unsafe ERC20 Operations should not be used](#l-3-unsafe-erc20-operations-should-not-be-used)
  - [L-4: Solidity pragma should be specific, not wide](#l-4-solidity-pragma-should-be-specific-not-wide)
  - [L-5: Missing checks for `address(0)` when assigning values to address state variables](#l-5-missing-checks-for-address0-when-assigning-values-to-address-state-variables)
  - [L-6: `public` functions not used internally could be marked `external`](#l-6-public-functions-not-used-internally-could-be-marked-external)
  - [L-7: Define and use `constant` variables instead of using literals](#l-7-define-and-use-constant-variables-instead-of-using-literals)
  - [L-8: Event is missing `indexed` fields](#l-8-event-is-missing-indexed-fields)
  - [L-9: PUSH0 is not supported by all chains](#l-9-push0-is-not-supported-by-all-chains)
  - [L-10: Modifiers invoked only once can be shoe-horned into the function](#l-10-modifiers-invoked-only-once-can-be-shoe-horned-into-the-function)
  - [L-11: Large literal values multiples of 10000 can be replaced with scientific notation](#l-11-large-literal-values-multiples-of-10000-can-be-replaced-with-scientific-notation)
  - [L-12: Internal functions called only once can be inlined](#l-12-internal-functions-called-only-once-can-be-inlined)
  - [L-13: Inconsistency in declaring uint256/uint (or) int256/int variables within a contract. Use explicit size declarations (uint256 or int256).](#l-13-inconsistency-in-declaring-uint256uint-or-int256int-variables-within-a-contract-use-explicit-size-declarations-uint256-or-int256)
  - [L-14: Loop contains `require`/`revert` statements](#l-14-loop-contains-requirerevert-statements)
  - [L-15: Boolean equality is not required.](#l-15-boolean-equality-is-not-required)


# Summary

## Files Summary

| Key | Value |
| --- | --- |
| .sol Files | 16 |
| Total nSLOC | 1480 |


## Files Details

| Filepath | nSLOC |
| --- | --- |
| src/DepositContract.sol | 110 |
| src/ERC20/ERC20PermitPermissionedMint.sol | 68 |
| src/OperatorRegistry.sol | 106 |
| src/Utils/Owned.sol | 26 |
| src/Utils/OwnedUpgradeable.sol | 28 |
| src/Utils/SigUtils.sol | 46 |
| src/frxETH.sol | 25 |
| src/frxETHMinter.sol | 93 |
| src/interfaces/IPlumeStaking.sol | 79 |
| src/interfaces/IStPlumeMinter.sol | 39 |
| src/interfaces/IsfrxETH.sol | 37 |
| src/interfaces/IstPlumeRewards.sol | 31 |
| src/interfaces/PlumeStakingStorage.sol | 100 |
| src/sfrxETH.sol | 49 |
| src/stPlumeMinter.sol | 492 |
| src/stPlumeRewards.sol | 151 |
| **Total** | **1480** |


## Issue Summary

| Category | No. of Issues |
| --- | --- |
| High | 6 |
| Low | 15 |


# High Issues

## H-1: Unsafe Casting

Downcasting int/uints in Solidity can be unsafe due to the potential for data loss and unintended behavior.When downcasting a larger integer type to a smaller one (e.g., uint256 to uint128), the value may exceed the range of the target type,leading to truncation and loss of significant digits. Use OpenZeppelin's SafeCast library to safely downcast integers.

<details><summary>6 Found Instances</summary>


- Found in src/stPlumeMinter.sol [Line: 381](src/stPlumeMinter.sol#L381)

	```solidity
	                plumeStaking.stake{value: _amount}(uint16(validatorId)); //stake stops 0 capacity from coming into here to cause infinite loops
	```

- Found in src/stPlumeMinter.sol [Line: 384](src/stPlumeMinter.sol#L384)

	```solidity
	                emit DepositSent(uint16(validatorId));
	```

- Found in src/stPlumeMinter.sol [Line: 406](src/stPlumeMinter.sol#L406)

	```solidity
	            plumeStaking.stake{value: depositSize}(uint16(validatorId)); //stake stops 0 capacity from coming into here to cause infinite loops
	```

- Found in src/stPlumeMinter.sol [Line: 410](src/stPlumeMinter.sol#L410)

	```solidity
	            emit DepositSent(uint16(validatorId));
	```

- Found in src/stPlumeMinter.sol [Line: 595](src/stPlumeMinter.sol#L595)

	```solidity
	        maxValidatorPercentage[uint16(_validatorId)] = _maxPercentage;
	```

- Found in src/stPlumeRewards.sol [Line: 178](src/stPlumeRewards.sol#L178)

	```solidity
	        rewardsCycleEnd = uint32(end);
	```

</details>



## H-2: Contract Name Reused in Different Files

When compiling contracts with certain development frameworks (for example: Truffle), having contracts with the same name across different files can lead to one being overwritten.

<details><summary>2 Found Instances</summary>


- Found in src/interfaces/IStPlumeMinter.sol [Line: 10](src/interfaces/IStPlumeMinter.sol#L10)

	```solidity
	interface IstPlumeMinter {
	```

- Found in src/interfaces/IstPlumeMinter.sol [Line: 10](src/interfaces/IStPlumeMinter.sol#L10)

	```solidity
	interface IstPlumeMinter {
	```

</details>



## H-3: Uninitialized State Variables

Solidity does initialize variables by default when you declare them, however it's good practice to explicitly declare an initial value. For example, if you transfer money to an address we must make sure that the address has been initialized.

<details><summary>7 Found Instances</summary>


- Found in src/frxETHMinter.sol [Line: 47](src/frxETHMinter.sol#L47)

	```solidity
	    IsfrxETH public sfrxETHToken;
	```

- Found in src/stPlumeMinter.sol [Line: 47](src/stPlumeMinter.sol#L47)

	```solidity
	    uint256 __gap1;
	```

- Found in src/stPlumeMinter.sol [Line: 48](src/stPlumeMinter.sol#L48)

	```solidity
	    uint256 __gap2;
	```

- Found in src/stPlumeMinter.sol [Line: 49](src/stPlumeMinter.sol#L49)

	```solidity
	    uint256 __gap3;
	```

- Found in src/stPlumeRewards.sol [Line: 36](src/stPlumeRewards.sol#L36)

	```solidity
	    uint256 __gap1;
	```

- Found in src/stPlumeRewards.sol [Line: 37](src/stPlumeRewards.sol#L37)

	```solidity
	    uint256 __gap2;
	```

- Found in src/stPlumeRewards.sol [Line: 38](src/stPlumeRewards.sol#L38)

	```solidity
	    uint256 __gap3;
	```

</details>



## H-4: Sending native Eth is not protected from these functions.

Introduce checks for `msg.sender` in the function

<details><summary>1 Found Instances</summary>


- Found in src/stPlumeMinter.sol [Line: 197](src/stPlumeMinter.sol#L197)

	```solidity
	    function withdraw(address recipient) external nonReentrant returns (uint256 amount) {
	```

</details>



## H-5: Return value of the function call is not checked.

Function returns a value but it is ignored.

<details><summary>10 Found Instances</summary>


- Found in src/frxETHMinter.sol [Line: 96](src/frxETHMinter.sol#L96)

	```solidity
	        _submit(msg.sender);
	```

- Found in src/frxETHMinter.sol [Line: 101](src/frxETHMinter.sol#L101)

	```solidity
	        _submit(recipient);
	```

- Found in src/frxETHMinter.sol [Line: 106](src/frxETHMinter.sol#L106)

	```solidity
	        _submit(msg.sender);
	```

- Found in src/stPlumeMinter.sol [Line: 94](src/stPlumeMinter.sol#L94)

	```solidity
	        _submit(msg.sender, validatorId);
	```

- Found in src/stPlumeMinter.sol [Line: 181](src/stPlumeMinter.sol#L181)

	```solidity
	        _depositEther(amount, validatorId);
	```

- Found in src/stPlumeMinter.sol [Line: 322](src/stPlumeMinter.sol#L322)

	```solidity
	        _unstake(yield, true, 0);
	```

- Found in src/stPlumeMinter.sol [Line: 524](src/stPlumeMinter.sol#L524)

	```solidity
	            plumeStaking.unstake(validatorId, amountToUnstake);
	```

- Found in src/stPlumeMinter.sol [Line: 546](src/stPlumeMinter.sol#L546)

	```solidity
	        _depositEther(amount, validatorId);
	```

- Found in src/stPlumeMinter.sol [Line: 615](src/stPlumeMinter.sol#L615)

	```solidity
	            _depositEther(msg.value, 0);
	```

- Found in src/stPlumeMinter.sol [Line: 621](src/stPlumeMinter.sol#L621)

	```solidity
	            _submit(msg.sender);
	```

</details>



## H-6: Contract locks Ether without a withdraw function.

It appears that the contract includes a payable function to accept Ether but lacks a corresponding function to withdraw it, which leads to the Ether being locked in the contract. To resolve this issue, please implement a public or external function that allows for the withdrawal of Ether from the contract.

<details><summary>2 Found Instances</summary>


- Found in src/DepositContract.sol [Line: 64](src/DepositContract.sol#L64)

	```solidity
	contract DepositContract is IDepositContract, ERC165 {
	```

- Found in src/stPlumeRewards.sol [Line: 19](src/stPlumeRewards.sol#L19)

	```solidity
	contract stPlumeRewards is Initializable, AccessControlUpgradeable, ReentrancyGuardUpgradeable, IstPlumeRewards {
	```

</details>



# Low Issues

## L-1: Centralization Risk for trusted owners

Contracts have owners with privileged rights to perform admin tasks and need to be trusted to not perform malicious updates or drain funds.

<details><summary>13 Found Instances</summary>


- Found in src/ERC20/ERC20PermitPermissionedMint.sol [Line: 14](src/ERC20/ERC20PermitPermissionedMint.sol#L14)

	```solidity
	contract ERC20PermitPermissionedMint is ERC20Permit, ERC20Burnable, Owned {
	```

- Found in src/Utils/Owned.sol [Line: 16](src/Utils/Owned.sol#L16)

	```solidity
	    function nominateNewOwner(address _owner) external onlyOwner {
	```

- Found in src/Utils/OwnedUpgradeable.sol [Line: 19](src/Utils/OwnedUpgradeable.sol#L19)

	```solidity
	    function nominateNewOwner(address _owner) external onlyOwner {
	```

- Found in src/frxETH.sol [Line: 56](src/frxETH.sol#L56)

	```solidity
	  function updateStPlumeRewards(address _stPlumeRewards) external onlyOwner {
	```

- Found in src/stPlumeMinter.sol [Line: 117](src/stPlumeMinter.sol#L117)

	```solidity
	    function rebalance() external nonReentrant onlyRole(REBALANCER_ROLE)  {
	```

- Found in src/stPlumeMinter.sol [Line: 141](src/stPlumeMinter.sol#L141)

	```solidity
	    function restake(uint16 validatorId) external nonReentrant onlyRole(REBALANCER_ROLE) returns (uint256 amountRestaked) {
	```

- Found in src/stPlumeMinter.sol [Line: 170](src/stPlumeMinter.sol#L170)

	```solidity
	    function stakeWitheld(uint256 amount) external nonReentrant onlyRole(REBALANCER_ROLE) returns (uint256 amountRestaked) {
	```

- Found in src/stPlumeMinter.sol [Line: 174](src/stPlumeMinter.sol#L174)

	```solidity
	    function stakeWitheldForValidator(uint256 amount, uint16 validatorId) public nonReentrant onlyRole(REBALANCER_ROLE) returns (uint256 amountRestaked) {
	```

- Found in src/stPlumeMinter.sol [Line: 286](src/stPlumeMinter.sol#L286)

	```solidity
	    function claim(uint16 validatorId) external nonReentrant onlyRole(CLAIMER_ROLE)  returns (uint256 amount) {
	```

- Found in src/stPlumeMinter.sol [Line: 301](src/stPlumeMinter.sol#L301)

	```solidity
	    function claimAll() external nonReentrant onlyRole(CLAIMER_ROLE)  returns (uint256[] memory amounts) {
	```

- Found in src/stPlumeRewards.sol [Line: 104](src/stPlumeRewards.sol#L104)

	```solidity
	    function handleTokenTransfer(address user) external onlyRole(HANDLER_ROLE) {
	```

- Found in src/stPlumeRewards.sol [Line: 182](src/stPlumeRewards.sol#L182)

	```solidity
	    function setYieldFee(uint256 newYieldFee) external onlyRole(DEFAULT_ADMIN_ROLE) {
	```

- Found in src/stPlumeRewards.sol [Line: 188](src/stPlumeRewards.sol#L188)

	```solidity
	    function setRewardsCycleLength(uint32 newLength) external onlyRole(DEFAULT_ADMIN_ROLE) {
	```

</details>



## L-2: Deprecated OpenZeppelin functions should not be used

Openzeppelin has deprecated several functions and replaced with newer versions. Please consult https://docs.openzeppelin.com/

<details><summary>8 Found Instances</summary>


- Found in src/stPlumeMinter.sol [Line: 71](src/stPlumeMinter.sol#L71)

	```solidity
	        _setupRole(DEFAULT_ADMIN_ROLE, _owner);
	```

- Found in src/stPlumeMinter.sol [Line: 72](src/stPlumeMinter.sol#L72)

	```solidity
	        _setupRole(REBALANCER_ROLE, _owner);
	```

- Found in src/stPlumeMinter.sol [Line: 73](src/stPlumeMinter.sol#L73)

	```solidity
	        _setupRole(CLAIMER_ROLE, _owner);
	```

- Found in src/stPlumeMinter.sol [Line: 74](src/stPlumeMinter.sol#L74)

	```solidity
	        _setupRole(HANDLER_ROLE, frxETHAddress);
	```

- Found in src/stPlumeRewards.sol [Line: 69](src/stPlumeRewards.sol#L69)

	```solidity
	        _setupRole(DEFAULT_ADMIN_ROLE, _admin);
	```

- Found in src/stPlumeRewards.sol [Line: 70](src/stPlumeRewards.sol#L70)

	```solidity
	        _setupRole(MINTER_ROLE, _admin);
	```

- Found in src/stPlumeRewards.sol [Line: 71](src/stPlumeRewards.sol#L71)

	```solidity
	        _setupRole(MINTER_ROLE, _stPlumeMinter);
	```

- Found in src/stPlumeRewards.sol [Line: 72](src/stPlumeRewards.sol#L72)

	```solidity
	        _setupRole(HANDLER_ROLE, _frxETHToken);
	```

</details>



## L-3: Unsafe ERC20 Operations should not be used

ERC20 functions may not behave as expected. For example: return values are not always meaningful. It is recommended to use OpenZeppelin's SafeERC20 library.

<details><summary>1 Found Instances</summary>


- Found in src/frxETHMinter.sol [Line: 152](src/frxETHMinter.sol#L152)

	```solidity
	        require(IERC20(tokenAddress).transfer(owner, tokenAmount), "recoverERC20: Transfer failed");
	```

</details>



## L-4: Solidity pragma should be specific, not wide

Consider using a specific version of Solidity in your contracts instead of a wide version. For example, instead of `pragma solidity ^0.8.0;`, use `pragma solidity 0.8.0;`

<details><summary>17 Found Instances</summary>


- Found in src/DepositContract.sol [Line: 12](src/DepositContract.sol#L12)

	```solidity
	pragma solidity ^0.8.0;
	```

- Found in src/ERC20/ERC20PermitPermissionedMint.sol [Line: 2](src/ERC20/ERC20PermitPermissionedMint.sol#L2)

	```solidity
	pragma solidity ^0.8.0;
	```

- Found in src/OperatorRegistry.sol [Line: 2](src/OperatorRegistry.sol#L2)

	```solidity
	pragma solidity ^0.8.0;
	```

- Found in src/Utils/Owned.sol [Line: 2](src/Utils/Owned.sol#L2)

	```solidity
	pragma solidity ^0.8.0;
	```

- Found in src/Utils/OwnedUpgradeable.sol [Line: 2](src/Utils/OwnedUpgradeable.sol#L2)

	```solidity
	pragma solidity ^0.8.0;
	```

- Found in src/Utils/SigUtils.sol [Line: 2](src/Utils/SigUtils.sol#L2)

	```solidity
	pragma solidity ^0.8.0;
	```

- Found in src/frxETH.sol [Line: 2](src/frxETH.sol#L2)

	```solidity
	pragma solidity ^0.8.0;
	```

- Found in src/frxETHMinter.sol [Line: 2](src/frxETHMinter.sol#L2)

	```solidity
	pragma solidity ^0.8.0;
	```

- Found in src/interfaces/IPlumeStaking.sol [Line: 3](src/interfaces/IPlumeStaking.sol#L3)

	```solidity
	pragma solidity ^0.8.25;
	```

- Found in src/interfaces/IStPlumeMinter.sol [Line: 2](src/interfaces/IStPlumeMinter.sol#L2)

	```solidity
	pragma solidity ^0.8.0;
	```

- Found in src/interfaces/IsfrxETH.sol [Line: 2](src/interfaces/IsfrxETH.sol#L2)

	```solidity
	pragma solidity >=0.8.0;
	```

- Found in src/interfaces/IstPlumeMinter.sol [Line: 2](src/interfaces/IStPlumeMinter.sol#L2)

	```solidity
	pragma solidity ^0.8.0;
	```

- Found in src/interfaces/IstPlumeRewards.sol [Line: 2](src/interfaces/IstPlumeRewards.sol#L2)

	```solidity
	pragma solidity ^0.8.0;
	```

- Found in src/interfaces/PlumeStakingStorage.sol [Line: 2](src/interfaces/PlumeStakingStorage.sol#L2)

	```solidity
	pragma solidity ^0.8.25;
	```

- Found in src/sfrxETH.sol [Line: 2](src/sfrxETH.sol#L2)

	```solidity
	pragma solidity ^0.8.0;
	```

- Found in src/stPlumeMinter.sol [Line: 2](src/stPlumeMinter.sol#L2)

	```solidity
	pragma solidity ^0.8.0;
	```

- Found in src/stPlumeRewards.sol [Line: 2](src/stPlumeRewards.sol#L2)

	```solidity
	pragma solidity ^0.8.0;
	```

</details>



## L-5: Missing checks for `address(0)` when assigning values to address state variables

Check for `address(0)` when assigning values to address state variables.

<details><summary>12 Found Instances</summary>


- Found in src/ERC20/ERC20PermitPermissionedMint.sol [Line: 34](src/ERC20/ERC20PermitPermissionedMint.sol#L34)

	```solidity
	      timelock_address = _timelock_address;
	```

- Found in src/OperatorRegistry.sol [Line: 42](src/OperatorRegistry.sol#L42)

	```solidity
	        timelock_address = _timelock_address;
	```

- Found in src/Utils/Owned.sol [Line: 17](src/Utils/Owned.sol#L17)

	```solidity
	        nominatedOwner = _owner;
	```

- Found in src/Utils/OwnedUpgradeable.sol [Line: 20](src/Utils/OwnedUpgradeable.sol#L20)

	```solidity
	        nominatedOwner = _owner;
	```

- Found in src/frxETH.sol [Line: 57](src/frxETH.sol#L57)

	```solidity
	    stPlumeRewards = _stPlumeRewards;
	```

- Found in src/frxETHMinter.sol [Line: 68](src/frxETHMinter.sol#L68)

	```solidity
	        depositContract = IDepositContract(depositContractAddress);
	```

- Found in src/frxETHMinter.sol [Line: 69](src/frxETHMinter.sol#L69)

	```solidity
	        frxETHToken = frxETH(frxETHAddress);
	```

- Found in src/stPlumeMinter.sol [Line: 70](src/stPlumeMinter.sol#L70)

	```solidity
	        plumeStaking = IPlumeStaking(_plumeStaking);
	```

- Found in src/stPlumeMinter.sol [Line: 606](src/stPlumeMinter.sol#L606)

	```solidity
	        nativeToken = _nativeToken;
	```

- Found in src/stPlumeMinter.sol [Line: 610](src/stPlumeMinter.sol#L610)

	```solidity
	        stPlumeRewards = IstPlumeRewards(_stPlumeRewards);
	```

- Found in src/stPlumeRewards.sol [Line: 62](src/stPlumeRewards.sol#L62)

	```solidity
	        frxETHToken = frxETH(_frxETHToken);
	```

- Found in src/stPlumeRewards.sol [Line: 63](src/stPlumeRewards.sol#L63)

	```solidity
	        stPlumeMinter = _stPlumeMinter;
	```

</details>



## L-6: `public` functions not used internally could be marked `external`

Instead of marking a function as `public`, consider marking it as `external` if it is not used internally.

<details><summary>18 Found Instances</summary>


- Found in src/ERC20/ERC20PermitPermissionedMint.sol [Line: 53](src/ERC20/ERC20PermitPermissionedMint.sol#L53)

	```solidity
	    function minter_burn_from(address b_address, uint256 b_amount) public onlyMinters {
	```

- Found in src/ERC20/ERC20PermitPermissionedMint.sol [Line: 59](src/ERC20/ERC20PermitPermissionedMint.sol#L59)

	```solidity
	    function minter_mint(address m_address, uint256 m_amount) public onlyMinters {
	```

- Found in src/ERC20/ERC20PermitPermissionedMint.sol [Line: 65](src/ERC20/ERC20PermitPermissionedMint.sol#L65)

	```solidity
	    function addMinter(address minter_address) public onlyByOwnGov {
	```

- Found in src/ERC20/ERC20PermitPermissionedMint.sol [Line: 76](src/ERC20/ERC20PermitPermissionedMint.sol#L76)

	```solidity
	    function removeMinter(address minter_address) public onlyByOwnGov {
	```

- Found in src/ERC20/ERC20PermitPermissionedMint.sol [Line: 94](src/ERC20/ERC20PermitPermissionedMint.sol#L94)

	```solidity
	    function setTimelock(address _timelock_address) public onlyByOwnGov {
	```

- Found in src/OperatorRegistry.sol [Line: 82](src/OperatorRegistry.sol#L82)

	```solidity
	    function popValidators(uint256 times) public onlyByOwnGov {
	```

- Found in src/OperatorRegistry.sol [Line: 93](src/OperatorRegistry.sol#L93)

	```solidity
	    function removeValidator(uint256 remove_idx, bool dont_care_about_ordering) public onlyByOwnGov {
	```

- Found in src/Utils/SigUtils.sol [Line: 44](src/Utils/SigUtils.sol#L44)

	```solidity
	    function getTypedDataHash(Permit memory _permit)
	```

- Found in src/sfrxETH.sol [Line: 59](src/sfrxETH.sol#L59)

	```solidity
	    function mint(uint256 shares, address receiver) public override andSync returns (uint256 assets) {
	```

- Found in src/sfrxETH.sol [Line: 64](src/sfrxETH.sol#L64)

	```solidity
	    function withdraw(
	```

- Found in src/sfrxETH.sol [Line: 73](src/sfrxETH.sol#L73)

	```solidity
	    function redeem(
	```

- Found in src/sfrxETH.sol [Line: 82](src/sfrxETH.sol#L82)

	```solidity
	    function pricePerShare() public view returns (uint256) {
	```

- Found in src/stPlumeMinter.sol [Line: 67](src/stPlumeMinter.sol#L67)

	```solidity
	    function initialize(address frxETHAddress, address _owner, address _timelock_address, address _plumeStaking) public initializer {
	```

- Found in src/stPlumeMinter.sol [Line: 85](src/stPlumeMinter.sol#L85)

	```solidity
	    function addValidator(Validator calldata validator) public override onlyByOwnGov {
	```

- Found in src/stPlumeMinter.sol [Line: 174](src/stPlumeMinter.sol#L174)

	```solidity
	    function stakeWitheldForValidator(uint256 amount, uint16 validatorId) public nonReentrant onlyRole(REBALANCER_ROLE) returns (uint256 amountRestaked) {
	```

- Found in src/stPlumeRewards.sol [Line: 56](src/stPlumeRewards.sol#L56)

	```solidity
	    function initialize(
	```

- Found in src/stPlumeRewards.sol [Line: 114](src/stPlumeRewards.sol#L114)

	```solidity
	    function getUserRewards(address user) public view returns (uint256 yield) {
	```

- Found in src/stPlumeRewards.sol [Line: 194](src/stPlumeRewards.sol#L194)

	```solidity
	    function getYield() public view returns (uint256) {
	```

</details>



## L-7: Define and use `constant` variables instead of using literals

If the same constant literal value is used multiple times, create a constant state variable and reference it throughout the contract.

<details><summary>4 Found Instances</summary>


- Found in src/DepositContract.sol [Line: 131](src/DepositContract.sol#L131)

	```solidity
	            sha256(abi.encodePacked(signature[:64])),
	```

- Found in src/DepositContract.sol [Line: 132](src/DepositContract.sol#L132)

	```solidity
	            sha256(abi.encodePacked(signature[64:], bytes32(0)))
	```

- Found in src/stPlumeMinter.sol [Line: 583](src/stPlumeMinter.sol#L583)

	```solidity
	        require(newInstantFee <= 1000000 && newStandardFee <= 1000000, "Fees too high");
	```

</details>



## L-8: Event is missing `indexed` fields

Index event fields make the field more quickly accessible to off-chain tools that parse events. However, note that each index field costs extra gas during emission, so it's not necessarily best to index the maximum allowed per event (three fields). Each event should use three indexed fields if there are three or more fields, and gas usage is not particularly of concern for the events in question. If there are fewer than three fields, all of the fields should be indexed.

<details><summary>30 Found Instances</summary>


- Found in src/DepositContract.sol [Line: 19](src/DepositContract.sol#L19)

	```solidity
	    event DepositEvent(
	```

- Found in src/ERC20/ERC20PermitPermissionedMint.sol [Line: 102](src/ERC20/ERC20PermitPermissionedMint.sol#L102)

	```solidity
	    event TokenMinterBurned(address indexed from, address indexed to, uint256 amount);
	```

- Found in src/ERC20/ERC20PermitPermissionedMint.sol [Line: 103](src/ERC20/ERC20PermitPermissionedMint.sol#L103)

	```solidity
	    event TokenMinterMinted(address indexed from, address indexed to, uint256 amount);
	```

- Found in src/ERC20/ERC20PermitPermissionedMint.sol [Line: 104](src/ERC20/ERC20PermitPermissionedMint.sol#L104)

	```solidity
	    event MinterAdded(address minter_address);
	```

- Found in src/ERC20/ERC20PermitPermissionedMint.sol [Line: 105](src/ERC20/ERC20PermitPermissionedMint.sol#L105)

	```solidity
	    event MinterRemoved(address minter_address);
	```

- Found in src/ERC20/ERC20PermitPermissionedMint.sol [Line: 106](src/ERC20/ERC20PermitPermissionedMint.sol#L106)

	```solidity
	    event TimelockChanged(address timelock_address);
	```

- Found in src/OperatorRegistry.sol [Line: 187](src/OperatorRegistry.sol#L187)

	```solidity
	    event TimelockChanged(address timelock_address);
	```

- Found in src/OperatorRegistry.sol [Line: 188](src/OperatorRegistry.sol#L188)

	```solidity
	    event WithdrawalCredentialSet(bytes _withdrawalCredential);
	```

- Found in src/OperatorRegistry.sol [Line: 189](src/OperatorRegistry.sol#L189)

	```solidity
	    event ValidatorAdded(uint256 validatorId, bytes withdrawalCredential);
	```

- Found in src/OperatorRegistry.sol [Line: 191](src/OperatorRegistry.sol#L191)

	```solidity
	    event ValidatorRemoved(uint256 validatorId, uint256 remove_idx, bool dont_care_about_ordering);
	```

- Found in src/OperatorRegistry.sol [Line: 192](src/OperatorRegistry.sol#L192)

	```solidity
	    event ValidatorsPopped(uint256 times);
	```

- Found in src/OperatorRegistry.sol [Line: 193](src/OperatorRegistry.sol#L193)

	```solidity
	    event ValidatorsSwapped(uint256 from_validatorId, uint256 to_validatorId, uint256 from_idx, uint256 to_idx);
	```

- Found in src/Utils/Owned.sol [Line: 33](src/Utils/Owned.sol#L33)

	```solidity
	    event OwnerNominated(address newOwner);
	```

- Found in src/Utils/Owned.sol [Line: 34](src/Utils/Owned.sol#L34)

	```solidity
	    event OwnerChanged(address oldOwner, address newOwner);
	```

- Found in src/Utils/OwnedUpgradeable.sol [Line: 36](src/Utils/OwnedUpgradeable.sol#L36)

	```solidity
	    event OwnerNominated(address newOwner);
	```

- Found in src/Utils/OwnedUpgradeable.sol [Line: 37](src/Utils/OwnedUpgradeable.sol#L37)

	```solidity
	    event OwnerChanged(address oldOwner, address newOwner);
	```

- Found in src/frxETHMinter.sol [Line: 157](src/frxETHMinter.sol#L157)

	```solidity
	    event EmergencyEtherRecovered(uint256 amount);
	```

- Found in src/frxETHMinter.sol [Line: 158](src/frxETHMinter.sol#L158)

	```solidity
	    event EmergencyERC20Recovered(address tokenAddress, uint256 tokenAmount);
	```

- Found in src/frxETHMinter.sol [Line: 159](src/frxETHMinter.sol#L159)

	```solidity
	    event ETHSubmitted(address indexed sender, address indexed recipient, uint256 sent_amount, uint256 withheld_amt);
	```

- Found in src/frxETHMinter.sol [Line: 160](src/frxETHMinter.sol#L160)

	```solidity
	    event DepositEtherPaused(bool new_status);
	```

- Found in src/frxETHMinter.sol [Line: 162](src/frxETHMinter.sol#L162)

	```solidity
	    event SubmitPaused(bool new_status);
	```

- Found in src/frxETHMinter.sol [Line: 163](src/frxETHMinter.sol#L163)

	```solidity
	    event WithheldETHMoved(address indexed to, uint256 amount);
	```

- Found in src/frxETHMinter.sol [Line: 164](src/frxETHMinter.sol#L164)

	```solidity
	    event WithholdRatioSet(uint256 newRatio);
	```

- Found in src/stPlumeMinter.sol [Line: 51](src/stPlumeMinter.sol#L51)

	```solidity
	    event Unstaked(address indexed user, uint256 amount);
	```

- Found in src/stPlumeMinter.sol [Line: 52](src/stPlumeMinter.sol#L52)

	```solidity
	    event Restaked(address indexed user, uint16 indexed validatorId, uint256 amount);
	```

- Found in src/stPlumeMinter.sol [Line: 53](src/stPlumeMinter.sol#L53)

	```solidity
	    event Withdrawn(address indexed user, uint256 amount);
	```

- Found in src/stPlumeMinter.sol [Line: 54](src/stPlumeMinter.sol#L54)

	```solidity
	    event RewardClaimed(address indexed user, address indexed token, uint256 amount);
	```

- Found in src/stPlumeMinter.sol [Line: 55](src/stPlumeMinter.sol#L55)

	```solidity
	    event AllRewardsClaimed(address indexed user, uint256[] totalAmount);
	```

- Found in src/stPlumeRewards.sol [Line: 48](src/stPlumeRewards.sol#L48)

	```solidity
	    event RewardClaimed(address indexed user, address indexed token, uint256 amount);
	```

- Found in src/stPlumeRewards.sol [Line: 49](src/stPlumeRewards.sol#L49)

	```solidity
	    event AllRewardsClaimed(address indexed user, uint256[] totalAmount);
	```

</details>



## L-9: PUSH0 is not supported by all chains

Solc compiler version 0.8.20 switches the default target EVM version to Shanghai, which means that the generated bytecode will include PUSH0 opcodes. Be sure to select the appropriate EVM version in case you intend to deploy on a chain other than mainnet like L2 chains that may not support PUSH0, otherwise deployment of your contracts will fail.

<details><summary>17 Found Instances</summary>


- Found in src/DepositContract.sol [Line: 12](src/DepositContract.sol#L12)

	```solidity
	pragma solidity ^0.8.0;
	```

- Found in src/ERC20/ERC20PermitPermissionedMint.sol [Line: 2](src/ERC20/ERC20PermitPermissionedMint.sol#L2)

	```solidity
	pragma solidity ^0.8.0;
	```

- Found in src/OperatorRegistry.sol [Line: 2](src/OperatorRegistry.sol#L2)

	```solidity
	pragma solidity ^0.8.0;
	```

- Found in src/Utils/Owned.sol [Line: 2](src/Utils/Owned.sol#L2)

	```solidity
	pragma solidity ^0.8.0;
	```

- Found in src/Utils/OwnedUpgradeable.sol [Line: 2](src/Utils/OwnedUpgradeable.sol#L2)

	```solidity
	pragma solidity ^0.8.0;
	```

- Found in src/Utils/SigUtils.sol [Line: 2](src/Utils/SigUtils.sol#L2)

	```solidity
	pragma solidity ^0.8.0;
	```

- Found in src/frxETH.sol [Line: 2](src/frxETH.sol#L2)

	```solidity
	pragma solidity ^0.8.0;
	```

- Found in src/frxETHMinter.sol [Line: 2](src/frxETHMinter.sol#L2)

	```solidity
	pragma solidity ^0.8.0;
	```

- Found in src/interfaces/IPlumeStaking.sol [Line: 3](src/interfaces/IPlumeStaking.sol#L3)

	```solidity
	pragma solidity ^0.8.25;
	```

- Found in src/interfaces/IStPlumeMinter.sol [Line: 2](src/interfaces/IStPlumeMinter.sol#L2)

	```solidity
	pragma solidity ^0.8.0;
	```

- Found in src/interfaces/IsfrxETH.sol [Line: 2](src/interfaces/IsfrxETH.sol#L2)

	```solidity
	pragma solidity >=0.8.0;
	```

- Found in src/interfaces/IstPlumeMinter.sol [Line: 2](src/interfaces/IStPlumeMinter.sol#L2)

	```solidity
	pragma solidity ^0.8.0;
	```

- Found in src/interfaces/IstPlumeRewards.sol [Line: 2](src/interfaces/IstPlumeRewards.sol#L2)

	```solidity
	pragma solidity ^0.8.0;
	```

- Found in src/interfaces/PlumeStakingStorage.sol [Line: 2](src/interfaces/PlumeStakingStorage.sol#L2)

	```solidity
	pragma solidity ^0.8.25;
	```

- Found in src/sfrxETH.sol [Line: 2](src/sfrxETH.sol#L2)

	```solidity
	pragma solidity ^0.8.0;
	```

- Found in src/stPlumeMinter.sol [Line: 2](src/stPlumeMinter.sol#L2)

	```solidity
	pragma solidity ^0.8.0;
	```

- Found in src/stPlumeRewards.sol [Line: 2](src/stPlumeRewards.sol#L2)

	```solidity
	pragma solidity ^0.8.0;
	```

</details>



## L-10: Modifiers invoked only once can be shoe-horned into the function



<details><summary>1 Found Instances</summary>


- Found in src/Utils/OwnedUpgradeable.sol [Line: 31](src/Utils/OwnedUpgradeable.sol#L31)

	```solidity
	    modifier onlyOwner {
	```

</details>



## L-11: Large literal values multiples of 10000 can be replaced with scientific notation

Use `e` notation, for example: `1e18`, instead of its full numeric value.

<details><summary>6 Found Instances</summary>


- Found in src/frxETHMinter.sol [Line: 70](src/frxETHMinter.sol#L70)

	```solidity
	        withholdRatio = 20000; // No ETH is withheld initially (2%)
	```

- Found in src/stPlumeMinter.sol [Line: 80](src/stPlumeMinter.sol#L80)

	```solidity
	        withdrawalQueueThreshold = 100000 ether;
	```

- Found in src/stPlumeMinter.sol [Line: 583](src/stPlumeMinter.sol#L583)

	```solidity
	        require(newInstantFee <= 1000000 && newStandardFee <= 1000000, "Fees too high");
	```

- Found in src/stPlumeRewards.sol [Line: 67](src/stPlumeRewards.sol#L67)

	```solidity
	        YIELD_FEE = 100000;
	```

- Found in src/stPlumeRewards.sol [Line: 183](src/stPlumeRewards.sol#L183)

	```solidity
	        require(newYieldFee <= 500000, "Fees too high");
	```

</details>



## L-12: Internal functions called only once can be inlined

Instead of separating the logic into a separate function, consider inlining the logic into the calling function. This can reduce the number of function calls and improve readability.

<details><summary>2 Found Instances</summary>


- Found in src/Utils/SigUtils.sol [Line: 25](src/Utils/SigUtils.sol#L25)

	```solidity
	    function getStructHash(Permit memory _permit)
	```

- Found in src/stPlumeMinter.sol [Line: 98](src/stPlumeMinter.sol#L98)

	```solidity
	    function getNextValidator(uint256 depositAmount, uint16 validatorId) internal view returns (uint256 validatorId_, uint256 capacity_) {
	```

</details>



## L-13: Inconsistency in declaring uint256/uint (or) int256/int variables within a contract. Use explicit size declarations (uint256 or int256).

Consider keeping the naming convention consistent in a given contract. Explicit size declarations are preferred (uint256, int256) over implicit ones (uint, int) to avoid confusion.

<details><summary>22 Found Instances</summary>


- Found in src/DepositContract.sol [Line: 65](src/DepositContract.sol#L65)

	```solidity
	    uint constant DEPOSIT_CONTRACT_TREE_DEPTH = 32;
	```

- Found in src/DepositContract.sol [Line: 67](src/DepositContract.sol#L67)

	```solidity
	    uint constant MAX_DEPOSIT_COUNT = 2**DEPOSIT_CONTRACT_TREE_DEPTH - 1;
	```

- Found in src/DepositContract.sol [Line: 76](src/DepositContract.sol#L76)

	```solidity
	        for (uint height = 0; height < DEPOSIT_CONTRACT_TREE_DEPTH - 1; height++)
	```

- Found in src/DepositContract.sol [Line: 82](src/DepositContract.sol#L82)

	```solidity
	        uint size = deposit_count;
	```

- Found in src/DepositContract.sol [Line: 83](src/DepositContract.sol#L83)

	```solidity
	        for (uint height = 0; height < DEPOSIT_CONTRACT_TREE_DEPTH; height++) {
	```

- Found in src/DepositContract.sol [Line: 115](src/DepositContract.sol#L115)

	```solidity
	        uint deposit_amount = msg.value / 1 gwei;
	```

- Found in src/DepositContract.sol [Line: 147](src/DepositContract.sol#L147)

	```solidity
	        uint size = deposit_count;
	```

- Found in src/DepositContract.sol [Line: 148](src/DepositContract.sol#L148)

	```solidity
	        for (uint height = 0; height < DEPOSIT_CONTRACT_TREE_DEPTH; height++) {
	```

- Found in src/ERC20/ERC20PermitPermissionedMint.sol [Line: 84](src/ERC20/ERC20PermitPermissionedMint.sol#L84)

	```solidity
	        for (uint i = 0; i < minters_array.length; i++){ 
	```

- Found in src/OperatorRegistry.sol [Line: 62](src/OperatorRegistry.sol#L62)

	```solidity
	        uint arrayLength = validatorArray.length;
	```

- Found in src/OperatorRegistry.sol [Line: 133](src/OperatorRegistry.sol#L133)

	```solidity
	        uint numVals = numValidators();
	```

- Found in src/OperatorRegistry.sol [Line: 145](src/OperatorRegistry.sol#L145)

	```solidity
	    function getValidator(uint i) 
	```

- Found in src/stPlumeMinter.sol [Line: 220](src/stPlumeMinter.sol#L220)

	```solidity
	        uint fee = totalAmount * INSTANT_REDEMPTION_FEE / RATIO_PRECISION;
	```

- Found in src/stPlumeMinter.sol [Line: 226](src/stPlumeMinter.sol#L226)

	```solidity
	        uint amountToWithdraw = totalAmount - fee;
	```

- Found in src/stPlumeMinter.sol [Line: 242](src/stPlumeMinter.sol#L242)

	```solidity
	        uint withdrawn = 0;
	```

- Found in src/stPlumeMinter.sol [Line: 246](src/stPlumeMinter.sol#L246)

	```solidity
	        uint fee = totalAmount * REDEMPTION_FEE / RATIO_PRECISION;
	```

- Found in src/stPlumeMinter.sol [Line: 271](src/stPlumeMinter.sol#L271)

	```solidity
	        uint amountToWithdraw = totalAmount - fee;
	```

- Found in src/stPlumeMinter.sol [Line: 330](src/stPlumeMinter.sol#L330)

	```solidity
	        uint numVals = numValidators();
	```

- Found in src/stPlumeMinter.sol [Line: 355](src/stPlumeMinter.sol#L355)

	```solidity
	            uint remainingAmount = info.maxCapacity - totalStaked;
	```

- Found in src/stPlumeMinter.sol [Line: 390](src/stPlumeMinter.sol#L390)

	```solidity
	        uint numVals = numValidators();
	```

- Found in src/stPlumeMinter.sol [Line: 463](src/stPlumeMinter.sol#L463)

	```solidity
	            uint numVals = numValidators();
	```

- Found in src/stPlumeMinter.sol [Line: 565](src/stPlumeMinter.sol#L565)

	```solidity
	        uint numVals = numValidators();
	```

</details>



## L-14: Loop contains `require`/`revert` statements

Avoid `require` / `revert` statements in a loop because a single bad item can cause the whole transaction to fail. It's better to forgive on fail and return failed elements post processing of the loop

<details><summary>1 Found Instances</summary>


- Found in src/stPlumeMinter.sol [Line: 464](src/stPlumeMinter.sol#L464)

	```solidity
	            while (index < numVals && _validatorId == 0) {
	```

</details>



## L-15: Boolean equality is not required.

If `x` is a boolean, there is no need to do `if(x == true)` or `if(x == false)`. Just use `if(x)` and `if(!x)` respectively.

<details><summary>3 Found Instances</summary>


- Found in src/ERC20/ERC20PermitPermissionedMint.sol [Line: 46](src/ERC20/ERC20PermitPermissionedMint.sol#L46)

	```solidity
	       require(minters[msg.sender] == true, "Only minters");
	```

- Found in src/ERC20/ERC20PermitPermissionedMint.sol [Line: 68](src/ERC20/ERC20PermitPermissionedMint.sol#L68)

	```solidity
	        require(minters[minter_address] == false, "Address already exists");
	```

- Found in src/ERC20/ERC20PermitPermissionedMint.sol [Line: 78](src/ERC20/ERC20PermitPermissionedMint.sol#L78)

	```solidity
	        require(minters[minter_address] == true, "Address nonexistant");
	```

</details>



