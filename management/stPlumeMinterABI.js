const abi = [{"type":"constructor","inputs":[{"name":"frxETHAddress","type":"address","internalType":"address"},{"name":"sfrxETHAddress","type":"address","internalType":"address"},{"name":"_owner","type":"address","internalType":"address"},{"name":"_timelock_address","type":"address","internalType":"address"},{"name":"_plumeStaking","type":"address","internalType":"address"}],"stateMutability":"nonpayable"},{"type":"receive","stateMutability":"payable"},{"type":"function","name":"CLAIMER_ROLE","inputs":[],"outputs":[{"name":"","type":"bytes32","internalType":"bytes32"}],"stateMutability":"view"},{"type":"function","name":"DEFAULT_ADMIN_ROLE","inputs":[],"outputs":[{"name":"","type":"bytes32","internalType":"bytes32"}],"stateMutability":"view"},{"type":"function","name":"DEPOSIT_SIZE","inputs":[],"outputs":[{"name":"","type":"uint256","internalType":"uint256"}],"stateMutability":"view"},{"type":"function","name":"HANDLER_ROLE","inputs":[],"outputs":[{"name":"","type":"bytes32","internalType":"bytes32"}],"stateMutability":"view"},{"type":"function","name":"INSTANT_REDEMPTION_FEE","inputs":[],"outputs":[{"name":"","type":"uint256","internalType":"uint256"}],"stateMutability":"view"},{"type":"function","name":"RATIO_PRECISION","inputs":[],"outputs":[{"name":"","type":"uint256","internalType":"uint256"}],"stateMutability":"view"},{"type":"function","name":"REBALANCER_ROLE","inputs":[],"outputs":[{"name":"","type":"bytes32","internalType":"bytes32"}],"stateMutability":"view"},{"type":"function","name":"REDEMPTION_FEE","inputs":[],"outputs":[{"name":"","type":"uint256","internalType":"uint256"}],"stateMutability":"view"},{"type":"function","name":"YIELD_FEE","inputs":[],"outputs":[{"name":"","type":"uint256","internalType":"uint256"}],"stateMutability":"view"},{"type":"function","name":"acceptOwnership","inputs":[],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"activeValidators","inputs":[{"name":"","type":"bytes","internalType":"bytes"}],"outputs":[{"name":"","type":"bool","internalType":"bool"}],"stateMutability":"view"},{"type":"function","name":"addValidator","inputs":[{"name":"validator","type":"tuple","internalType":"struct OperatorRegistry.Validator","components":[{"name":"validatorId","type":"uint256","internalType":"uint256"}]}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"addValidators","inputs":[{"name":"validatorArray","type":"tuple[]","internalType":"struct OperatorRegistry.Validator[]","components":[{"name":"validatorId","type":"uint256","internalType":"uint256"}]}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"batchUnstakeInterval","inputs":[],"outputs":[{"name":"","type":"uint256","internalType":"uint256"}],"stateMutability":"view"},{"type":"function","name":"claim","inputs":[{"name":"validatorId","type":"uint16","internalType":"uint16"}],"outputs":[{"name":"amount","type":"uint256","internalType":"uint256"}],"stateMutability":"nonpayable"},{"type":"function","name":"claimAll","inputs":[],"outputs":[{"name":"amounts","type":"uint256[]","internalType":"uint256[]"}],"stateMutability":"nonpayable"},{"type":"function","name":"clearValidatorArray","inputs":[],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"currentWithheldETH","inputs":[],"outputs":[{"name":"","type":"uint256","internalType":"uint256"}],"stateMutability":"view"},{"type":"function","name":"cycleRewards","inputs":[{"name":"","type":"uint256","internalType":"uint256"}],"outputs":[{"name":"rewards","type":"uint256","internalType":"uint256"},{"name":"totalSupply","type":"uint256","internalType":"uint256"},{"name":"cycleEnd","type":"uint32","internalType":"uint32"}],"stateMutability":"view"},{"type":"function","name":"depositContract","inputs":[],"outputs":[{"name":"","type":"address","internalType":"contract IDepositContract"}],"stateMutability":"view"},{"type":"function","name":"depositEtherPaused","inputs":[],"outputs":[{"name":"","type":"bool","internalType":"bool"}],"stateMutability":"view"},{"type":"function","name":"frxETHToken","inputs":[],"outputs":[{"name":"","type":"address","internalType":"contract frxETH"}],"stateMutability":"view"},{"type":"function","name":"getClaimableReward","inputs":[],"outputs":[{"name":"amount","type":"uint256","internalType":"uint256"}],"stateMutability":"nonpayable"},{"type":"function","name":"getNextValidator","inputs":[{"name":"depositAmount","type":"uint256","internalType":"uint256"},{"name":"validatorId","type":"uint16","internalType":"uint16"}],"outputs":[{"name":"validatorId_","type":"uint256","internalType":"uint256"},{"name":"capacity_","type":"uint256","internalType":"uint256"}],"stateMutability":"view"},{"type":"function","name":"getRewardRate","inputs":[],"outputs":[{"name":"rate","type":"uint256","internalType":"uint256"}],"stateMutability":"view"},{"type":"function","name":"getRoleAdmin","inputs":[{"name":"role","type":"bytes32","internalType":"bytes32"}],"outputs":[{"name":"","type":"bytes32","internalType":"bytes32"}],"stateMutability":"view"},{"type":"function","name":"getUserRewards","inputs":[{"name":"user","type":"address","internalType":"address"}],"outputs":[{"name":"yield","type":"uint256","internalType":"uint256"}],"stateMutability":"view"},{"type":"function","name":"getUserValidators","inputs":[{"name":"user","type":"address","internalType":"address"}],"outputs":[{"name":"validatorIds","type":"uint16[]","internalType":"uint16[]"}],"stateMutability":"view"},{"type":"function","name":"getValidator","inputs":[{"name":"i","type":"uint256","internalType":"uint256"}],"outputs":[{"name":"validatorId","type":"uint256","internalType":"uint256"}],"stateMutability":"view"},{"type":"function","name":"getValidatorStake","inputs":[{"name":"validatorId","type":"uint16","internalType":"uint16"}],"outputs":[{"name":"","type":"uint256","internalType":"uint256"}],"stateMutability":"view"},{"type":"function","name":"getValidatorStats","inputs":[{"name":"validatorId","type":"uint16","internalType":"uint16"}],"outputs":[{"name":"active","type":"bool","internalType":"bool"},{"name":"commission","type":"uint256","internalType":"uint256"},{"name":"totalStaked","type":"uint256","internalType":"uint256"},{"name":"stakersCount","type":"uint256","internalType":"uint256"}],"stateMutability":"view"},{"type":"function","name":"getValidatorStruct","inputs":[{"name":"validatorId","type":"uint256","internalType":"uint256"}],"outputs":[{"name":"","type":"tuple","internalType":"struct OperatorRegistry.Validator","components":[{"name":"validatorId","type":"uint256","internalType":"uint256"}]}],"stateMutability":"pure"},{"type":"function","name":"getYield","inputs":[],"outputs":[{"name":"","type":"uint256","internalType":"uint256"}],"stateMutability":"view"},{"type":"function","name":"grantRole","inputs":[{"name":"role","type":"bytes32","internalType":"bytes32"},{"name":"account","type":"address","internalType":"address"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"handleTokenTransfer","inputs":[{"name":"user","type":"address","internalType":"address"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"hasRole","inputs":[{"name":"role","type":"bytes32","internalType":"bytes32"},{"name":"account","type":"address","internalType":"address"}],"outputs":[{"name":"","type":"bool","internalType":"bool"}],"stateMutability":"view"},{"type":"function","name":"lastRewardAmount","inputs":[],"outputs":[{"name":"","type":"uint256","internalType":"uint256"}],"stateMutability":"view"},{"type":"function","name":"lastSync","inputs":[],"outputs":[{"name":"","type":"uint32","internalType":"uint32"}],"stateMutability":"view"},{"type":"function","name":"loadRewards","inputs":[],"outputs":[{"name":"amount","type":"uint256","internalType":"uint256"}],"stateMutability":"payable"},{"type":"function","name":"maxValidatorPercentage","inputs":[{"name":"","type":"uint16","internalType":"uint16"}],"outputs":[{"name":"","type":"uint256","internalType":"uint256"}],"stateMutability":"view"},{"type":"function","name":"minStake","inputs":[],"outputs":[{"name":"","type":"uint256","internalType":"uint256"}],"stateMutability":"view"},{"type":"function","name":"moveWithheldETH","inputs":[{"name":"to","type":"address","internalType":"address payable"},{"name":"amount","type":"uint256","internalType":"uint256"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"nativeToken","inputs":[],"outputs":[{"name":"","type":"address","internalType":"address"}],"stateMutability":"view"},{"type":"function","name":"nextBatchUnstakeTimePerValidator","inputs":[{"name":"","type":"uint16","internalType":"uint16"}],"outputs":[{"name":"","type":"uint256","internalType":"uint256"}],"stateMutability":"view"},{"type":"function","name":"nominateNewOwner","inputs":[{"name":"_owner","type":"address","internalType":"address"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"nominatedOwner","inputs":[],"outputs":[{"name":"","type":"address","internalType":"address"}],"stateMutability":"view"},{"type":"function","name":"normalizedAmount","inputs":[{"name":"user","type":"address","internalType":"address"},{"name":"amount","type":"uint256","internalType":"uint256"}],"outputs":[{"name":"","type":"uint256","internalType":"uint256"}],"stateMutability":"view"},{"type":"function","name":"numValidators","inputs":[],"outputs":[{"name":"","type":"uint256","internalType":"uint256"}],"stateMutability":"view"},{"type":"function","name":"owner","inputs":[],"outputs":[{"name":"","type":"address","internalType":"address"}],"stateMutability":"view"},{"type":"function","name":"popValidators","inputs":[{"name":"times","type":"uint256","internalType":"uint256"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"processBatchUnstake","inputs":[],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"rebalance","inputs":[],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"recoverERC20","inputs":[{"name":"tokenAddress","type":"address","internalType":"address"},{"name":"tokenAmount","type":"uint256","internalType":"uint256"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"recoverEther","inputs":[{"name":"amount","type":"uint256","internalType":"uint256"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"removeValidator","inputs":[{"name":"remove_idx","type":"uint256","internalType":"uint256"},{"name":"dont_care_about_ordering","type":"bool","internalType":"bool"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"renounceRole","inputs":[{"name":"role","type":"bytes32","internalType":"bytes32"},{"name":"account","type":"address","internalType":"address"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"restake","inputs":[{"name":"validatorId","type":"uint16","internalType":"uint16"}],"outputs":[{"name":"amountRestaked","type":"uint256","internalType":"uint256"}],"stateMutability":"nonpayable"},{"type":"function","name":"revokeRole","inputs":[{"name":"role","type":"bytes32","internalType":"bytes32"},{"name":"account","type":"address","internalType":"address"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"rewardsCycleEnd","inputs":[],"outputs":[{"name":"","type":"uint32","internalType":"uint32"}],"stateMutability":"view"},{"type":"function","name":"rewardsCycleLength","inputs":[],"outputs":[{"name":"","type":"uint32","internalType":"uint32"}],"stateMutability":"view"},{"type":"function","name":"rewardsEth","inputs":[],"outputs":[{"name":"","type":"uint256","internalType":"uint256"}],"stateMutability":"view"},{"type":"function","name":"setBatchUnstakeParams","inputs":[{"name":"_threshold","type":"uint256","internalType":"uint256"},{"name":"_interval","type":"uint256","internalType":"uint256"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"setMaxValidatorPercentage","inputs":[{"name":"_validatorId","type":"uint256","internalType":"uint256"},{"name":"_maxPercentage","type":"uint256","internalType":"uint256"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"setMinStake","inputs":[{"name":"_minStake","type":"uint256","internalType":"uint256"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"setNativeToken","inputs":[{"name":"_nativeToken","type":"address","internalType":"address"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"setRedemptionFees","inputs":[{"name":"newInstantFee","type":"uint256","internalType":"uint256"},{"name":"newStandardFee","type":"uint256","internalType":"uint256"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"setRewardsCycleLength","inputs":[{"name":"newLength","type":"uint32","internalType":"uint32"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"setTimelock","inputs":[{"name":"_timelock_address","type":"address","internalType":"address"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"setWithholdRatio","inputs":[{"name":"newRatio","type":"uint256","internalType":"uint256"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"setYieldFee","inputs":[{"name":"newFee","type":"uint256","internalType":"uint256"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"sfrxETHToken","inputs":[],"outputs":[{"name":"","type":"address","internalType":"contract IsfrxETH"}],"stateMutability":"view"},{"type":"function","name":"stakeInfo","inputs":[],"outputs":[{"name":"","type":"tuple","internalType":"struct PlumeStakingStorage.StakeInfo","components":[{"name":"staked","type":"uint256","internalType":"uint256"},{"name":"cooled","type":"uint256","internalType":"uint256"},{"name":"parked","type":"uint256","internalType":"uint256"}]}],"stateMutability":"view"},{"type":"function","name":"stakeWitheld","inputs":[{"name":"amount","type":"uint256","internalType":"uint256"}],"outputs":[{"name":"amountRestaked","type":"uint256","internalType":"uint256"}],"stateMutability":"nonpayable"},{"type":"function","name":"submit","inputs":[],"outputs":[],"stateMutability":"payable"},{"type":"function","name":"submitAndGive","inputs":[{"name":"recipient","type":"address","internalType":"address"}],"outputs":[],"stateMutability":"payable"},{"type":"function","name":"submitForValidator","inputs":[{"name":"validatorId","type":"uint16","internalType":"uint16"}],"outputs":[],"stateMutability":"payable"},{"type":"function","name":"submitPaused","inputs":[],"outputs":[{"name":"","type":"bool","internalType":"bool"}],"stateMutability":"view"},{"type":"function","name":"supportsInterface","inputs":[{"name":"interfaceId","type":"bytes4","internalType":"bytes4"}],"outputs":[{"name":"","type":"bool","internalType":"bool"}],"stateMutability":"view"},{"type":"function","name":"swapValidator","inputs":[{"name":"from_idx","type":"uint256","internalType":"uint256"},{"name":"to_idx","type":"uint256","internalType":"uint256"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"syncRewards","inputs":[],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"timelock_address","inputs":[],"outputs":[{"name":"","type":"address","internalType":"address"}],"stateMutability":"view"},{"type":"function","name":"togglePauseDepositEther","inputs":[],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"togglePauseSubmits","inputs":[],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"totalAmountClaimable","inputs":[],"outputs":[{"name":"amount","type":"uint256","internalType":"uint256"}],"stateMutability":"view"},{"type":"function","name":"totalInstantUnstaked","inputs":[],"outputs":[{"name":"","type":"uint256","internalType":"uint256"}],"stateMutability":"view"},{"type":"function","name":"totalQueuedWithdrawalsPerValidator","inputs":[{"name":"","type":"uint16","internalType":"uint16"}],"outputs":[{"name":"","type":"uint256","internalType":"uint256"}],"stateMutability":"view"},{"type":"function","name":"totalUnstaked","inputs":[],"outputs":[{"name":"","type":"uint256","internalType":"uint256"}],"stateMutability":"view"},{"type":"function","name":"unstake","inputs":[{"name":"amount","type":"uint256","internalType":"uint256"}],"outputs":[{"name":"amountUnstaked","type":"uint256","internalType":"uint256"}],"stateMutability":"nonpayable"},{"type":"function","name":"unstakeFromValidator","inputs":[{"name":"amount","type":"uint256","internalType":"uint256"},{"name":"validatorId","type":"uint16","internalType":"uint16"}],"outputs":[{"name":"amountUnstaked","type":"uint256","internalType":"uint256"}],"stateMutability":"nonpayable"},{"type":"function","name":"unstakeGov","inputs":[{"name":"validatorId","type":"uint16","internalType":"uint16"},{"name":"amount","type":"uint256","internalType":"uint256"}],"outputs":[{"name":"amountRestaked","type":"uint256","internalType":"uint256"}],"stateMutability":"nonpayable"},{"type":"function","name":"unstakeRewards","inputs":[],"outputs":[{"name":"yield","type":"uint256","internalType":"uint256"}],"stateMutability":"nonpayable"},{"type":"function","name":"userRewards","inputs":[{"name":"","type":"address","internalType":"address"}],"outputs":[{"name":"rewardInCycle","type":"uint256","internalType":"uint256"},{"name":"rewardsBefore","type":"uint256","internalType":"uint256"},{"name":"rewardsAccrued","type":"uint256","internalType":"uint256"},{"name":"lastCycleClaimed","type":"uint256","internalType":"uint256"}],"stateMutability":"view"},{"type":"function","name":"withHoldEth","inputs":[],"outputs":[{"name":"","type":"uint256","internalType":"uint256"}],"stateMutability":"view"},{"type":"function","name":"withdraw","inputs":[{"name":"recipient","type":"address","internalType":"address"}],"outputs":[{"name":"amount","type":"uint256","internalType":"uint256"}],"stateMutability":"nonpayable"},{"type":"function","name":"withdrawFee","inputs":[],"outputs":[{"name":"amount","type":"uint256","internalType":"uint256"}],"stateMutability":"nonpayable"},{"type":"function","name":"withdrawGov","inputs":[{"name":"amount","type":"uint256","internalType":"uint256"}],"outputs":[{"name":"amountWithdrawn","type":"uint256","internalType":"uint256"}],"stateMutability":"nonpayable"},{"type":"function","name":"withdrawalQueueThreshold","inputs":[],"outputs":[{"name":"","type":"uint256","internalType":"uint256"}],"stateMutability":"view"},{"type":"function","name":"withdrawalRequests","inputs":[{"name":"","type":"address","internalType":"address"}],"outputs":[{"name":"amount","type":"uint256","internalType":"uint256"},{"name":"deficit","type":"uint256","internalType":"uint256"},{"name":"timestamp","type":"uint256","internalType":"uint256"}],"stateMutability":"view"},{"type":"function","name":"withholdRatio","inputs":[],"outputs":[{"name":"","type":"uint256","internalType":"uint256"}],"stateMutability":"view"},{"type":"function","name":"yieldEth","inputs":[],"outputs":[{"name":"","type":"uint256","internalType":"uint256"}],"stateMutability":"view"},{"type":"event","name":"AllRewardsClaimed","inputs":[{"name":"user","type":"address","indexed":true,"internalType":"address"},{"name":"totalAmount","type":"uint256[]","indexed":false,"internalType":"uint256[]"}],"anonymous":false},{"type":"event","name":"DepositEtherPaused","inputs":[{"name":"new_status","type":"bool","indexed":false,"internalType":"bool"}],"anonymous":false},{"type":"event","name":"DepositSent","inputs":[{"name":"validatorId","type":"uint16","indexed":true,"internalType":"uint16"}],"anonymous":false},{"type":"event","name":"ETHSubmitted","inputs":[{"name":"sender","type":"address","indexed":true,"internalType":"address"},{"name":"recipient","type":"address","indexed":true,"internalType":"address"},{"name":"sent_amount","type":"uint256","indexed":false,"internalType":"uint256"},{"name":"withheld_amt","type":"uint256","indexed":false,"internalType":"uint256"}],"anonymous":false},{"type":"event","name":"EmergencyERC20Recovered","inputs":[{"name":"tokenAddress","type":"address","indexed":false,"internalType":"address"},{"name":"tokenAmount","type":"uint256","indexed":false,"internalType":"uint256"}],"anonymous":false},{"type":"event","name":"EmergencyEtherRecovered","inputs":[{"name":"amount","type":"uint256","indexed":false,"internalType":"uint256"}],"anonymous":false},{"type":"event","name":"KeysCleared","inputs":[],"anonymous":false},{"type":"event","name":"OwnerChanged","inputs":[{"name":"oldOwner","type":"address","indexed":false,"internalType":"address"},{"name":"newOwner","type":"address","indexed":false,"internalType":"address"}],"anonymous":false},{"type":"event","name":"OwnerNominated","inputs":[{"name":"newOwner","type":"address","indexed":false,"internalType":"address"}],"anonymous":false},{"type":"event","name":"Restaked","inputs":[{"name":"user","type":"address","indexed":true,"internalType":"address"},{"name":"validatorId","type":"uint16","indexed":true,"internalType":"uint16"},{"name":"amount","type":"uint256","indexed":false,"internalType":"uint256"}],"anonymous":false},{"type":"event","name":"RewardClaimed","inputs":[{"name":"user","type":"address","indexed":true,"internalType":"address"},{"name":"token","type":"address","indexed":true,"internalType":"address"},{"name":"amount","type":"uint256","indexed":false,"internalType":"uint256"}],"anonymous":false},{"type":"event","name":"RoleAdminChanged","inputs":[{"name":"role","type":"bytes32","indexed":true,"internalType":"bytes32"},{"name":"previousAdminRole","type":"bytes32","indexed":true,"internalType":"bytes32"},{"name":"newAdminRole","type":"bytes32","indexed":true,"internalType":"bytes32"}],"anonymous":false},{"type":"event","name":"RoleGranted","inputs":[{"name":"role","type":"bytes32","indexed":true,"internalType":"bytes32"},{"name":"account","type":"address","indexed":true,"internalType":"address"},{"name":"sender","type":"address","indexed":true,"internalType":"address"}],"anonymous":false},{"type":"event","name":"RoleRevoked","inputs":[{"name":"role","type":"bytes32","indexed":true,"internalType":"bytes32"},{"name":"account","type":"address","indexed":true,"internalType":"address"},{"name":"sender","type":"address","indexed":true,"internalType":"address"}],"anonymous":false},{"type":"event","name":"SubmitPaused","inputs":[{"name":"new_status","type":"bool","indexed":false,"internalType":"bool"}],"anonymous":false},{"type":"event","name":"TimelockChanged","inputs":[{"name":"timelock_address","type":"address","indexed":false,"internalType":"address"}],"anonymous":false},{"type":"event","name":"Unstaked","inputs":[{"name":"user","type":"address","indexed":true,"internalType":"address"},{"name":"amount","type":"uint256","indexed":false,"internalType":"uint256"}],"anonymous":false},{"type":"event","name":"ValidatorAdded","inputs":[{"name":"validatorId","type":"uint256","indexed":false,"internalType":"uint256"},{"name":"withdrawalCredential","type":"bytes","indexed":false,"internalType":"bytes"}],"anonymous":false},{"type":"event","name":"ValidatorArrayCleared","inputs":[],"anonymous":false},{"type":"event","name":"ValidatorRemoved","inputs":[{"name":"validatorId","type":"uint256","indexed":false,"internalType":"uint256"},{"name":"remove_idx","type":"uint256","indexed":false,"internalType":"uint256"},{"name":"dont_care_about_ordering","type":"bool","indexed":false,"internalType":"bool"}],"anonymous":false},{"type":"event","name":"ValidatorRewardClaimed","inputs":[{"name":"user","type":"address","indexed":true,"internalType":"address"},{"name":"token","type":"address","indexed":true,"internalType":"address"},{"name":"validatorId","type":"uint16","indexed":true,"internalType":"uint16"},{"name":"amount","type":"uint256","indexed":false,"internalType":"uint256"}],"anonymous":false},{"type":"event","name":"ValidatorsPopped","inputs":[{"name":"times","type":"uint256","indexed":false,"internalType":"uint256"}],"anonymous":false},{"type":"event","name":"ValidatorsSwapped","inputs":[{"name":"from_validatorId","type":"uint256","indexed":false,"internalType":"uint256"},{"name":"to_validatorId","type":"uint256","indexed":false,"internalType":"uint256"},{"name":"from_idx","type":"uint256","indexed":false,"internalType":"uint256"},{"name":"to_idx","type":"uint256","indexed":false,"internalType":"uint256"}],"anonymous":false},{"type":"event","name":"WithdrawalCredentialSet","inputs":[{"name":"_withdrawalCredential","type":"bytes","indexed":false,"internalType":"bytes"}],"anonymous":false},{"type":"event","name":"Withdrawn","inputs":[{"name":"user","type":"address","indexed":true,"internalType":"address"},{"name":"amount","type":"uint256","indexed":false,"internalType":"uint256"}],"anonymous":false},{"type":"event","name":"WithheldETHMoved","inputs":[{"name":"to","type":"address","indexed":true,"internalType":"address"},{"name":"amount","type":"uint256","indexed":false,"internalType":"uint256"}],"anonymous":false},{"type":"event","name":"WithholdRatioSet","inputs":[{"name":"newRatio","type":"uint256","indexed":false,"internalType":"uint256"}],"anonymous":false}]

module.exports = abi