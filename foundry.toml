[profile.default]
src = 'src'
out = 'out'
libs = ['lib']
via-ir = false
optimizer = true
optimizer_runs = 10
fs_permissions = [{ access = "read", path = "./"}]


[fuzz]
# https://book.getfoundry.sh/reference/config/testing#fuzz
runs = 256
#runs = 10000
max_test_rejects = 65536
seed = '0x3e8'
dictionary_weight = 40
include_storage = true
include_push_bytes = true

# See more config options https://github.com/foundry-rs/foundry/tree/master/config